# HK Time Audit – Locations where `new Date()` is passed to `saveMessageWithSP`

> Regex used: `saveMessageWithSP[^(]*\([^)]*new\s+Date\(`

---

### 1. apps/api/src/general/chat/chat-completion/chat-completion.service.ts (lines ~970-990)
```ts
        const savedUserMessage = await this.saveMessageWithSP(
          uuidv4(),
          currentConversationUUID,
          contentToSave,
          // Use effective temperature for saving
          effectiveTemperatureForSaving,
          llmConfig.instanceName ?? llmConfig.region ?? 'N/A',
          modelIdentifierForSaving,
          'user',
          0,
          userId,
          new Date(),           // ← new Date() passed
          undefined,
          usedMention,
        );
```

---

### 2. apps/api/src/general/chat/chat-completion/chat-completion.service.ts (lines ~1040-1058)
```ts
          const savedUserMessage = await this.saveMessageWithSP(
            uuidv4(),
            currentConversationUUID,
            contentToSave,
            effectiveTemperatureForSaving,
            llmConfig.instanceName ?? llmConfig.region ?? 'N/A',
            modelIdentifierForSaving,
            'user',
            0,
            userId,
            new Date(),          // ← new Date() passed
            undefined,
            usedMention,
          );
```

---

### 3. apps/api/src/general/chat/chat-completion/chat-completion.service.ts (lines ~1500-1516)
```ts
        const savedAiMessage = await this.saveMessageWithSP(
          aiMessageUUID,
          currentConversationUUID,
          aiContent,
          effectiveTemperature, // Use temp for saving
          llmConfig.instanceName ?? llmConfig.region ?? 'N/A',
          modelIdentifierForSaving,
          'assistant',
          completionTokens,
          user.userId,
          new Date(),           // ← new Date() passed
          undefined,
          undefined, // Assistant messages don't use @mention
        );
```

---

### 4. apps/api/src/general/chat/chat-completion/chat-completion.service.ts (lines ~2290-2308)
```ts
        if (finalAiContent) {
          const savedAiMessage = await this.saveMessageWithSP(
            aiMessageUUID,
            conversationUUID,
            finalAiContent,
            temperature,
            llmConfig.instanceName ?? llmConfig.region ?? 'N/A',
            modelIdentifier,
            'assistant',
            completionTokens,
            userId,
            new Date(),          // ← new Date() passed
            undefined,
            undefined, // Assistant messages don't use @mention
          );
```

---

No other occurrences were found where `new Date()` is passed directly to `saveMessageWithSP`.

